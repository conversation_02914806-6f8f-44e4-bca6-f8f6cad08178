#!/usr/bin/env python3
import os
import json
from pathlib import Path
import re
import sys
import argparse

# --- Configuration ---
# ROOT_PATH = os.getcwd()
ROOT_PATH = Path(os.path.abspath(__file__)).parent.parent
DOCS_PATH = os.path.join(ROOT_PATH, "docs")

FOLDERS_TO_IGNORE = {
    ".claude",
    ".kilocode",
    ".husky",
    ".continue",
    ".augment",
    "coverage",
    "html",
    ".mypy_cache",
    "test-results",
    "playwright-report",
    ".next",
    ".gemini",
    ".github",
    ".benchmarks",
    ".history",
    ".vscode",
    ".venv",
    ".pytest_cache",
    ".ruff_cache",
    "node_modules",
    "htmlcov",
    "versions",
    "bin",
    "logs",
    "migrations",
    "obj",
    "dist",
    "build",
    ".git",
    "target",
    "vendor",
    "test_logs",
    "test_reports",
    "__pycache__",
    "$RECYCLE.BIN",
    "System Volume Information",
    ".tmp",
    ".trae",
    "ued_api_server.egg-info",
    "dev",
    ".roo",
    "site",
}

FILE_NAMES_TO_IGNORE = {
    "__init__.py",
    ".gitignore",
    ".gitattributes",
    "Thumbs.db",
    "env.py",
    "alembic.ini",
    "coverage.xml",
    "mypy.ini",
    "server.code-workspace",
    "structure.md",
    "desktop.ini",
    "config.json.example",
    ".coverage",
    ".kilocodemodes",
    "script.py.mako",
    "pnpm-lock.yaml",
    "tsconfig.tsbuildinfo",
    "poetry.toml",
    "poetry.lock",
    ".prettierrc",
    ".dockerignore",
    ".python-version",
    ".safety-project.ini",
    ".lintstagedrc.mjs",
    ".npmrc",
    ".prettierignore",
    "next-env.d.ts",
    "next.config.ts",
    "ued.code-workspace",
    "prettier.config.js",
    ".markdownlint.json",
    "CLAUDE.md",
    "scan-structure.py",
    ".roomodes",
}

FILE_EXTENSIONS_TO_IGNORE = {
    ".pyc",
    ".tmp",
    ".log",
    ".bak",
    ".swp",
    ".json",
    ".ps1",
    ".html",
    ".lock",
    ".yaml",
    ".txt",
    ".mjs",
    ".yml",
    ".css",
    ".ico",
    ".png",
    ".svg",
    ".js",
}

# --- Comment Extraction Logic ---


def get_py_comment(file_path):
    """Extracts the first line of the docstring from a Python file."""
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            content = f.read(2048)
        match = re.search(
            r'^(?:#.*(?:\r?\n))*\s*("""([\s\S]*?)"""|\'\'\'([\s\S]*?)\'\'\')', content
        )
        if match:
            docstring = match.group(2) if match.group(2) is not None else match.group(3)
            first_line = docstring.strip().splitlines()[0].strip()
            return first_line[:97] + "..." if len(first_line) > 100 else first_line
    except Exception:
        pass
    return None


def get_ts_comment(file_path):
    """Extracts the first line of the JSDoc comment from a TypeScript file."""
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            content = f.read(4096)
        match = re.search(r"/\*\*\s*([\s\S]*?)\s*\*/", content)
        if match:
            comment_block = match.group(1)
            lines = [line.strip() for line in comment_block.strip().splitlines()]
            summary_line = ""
            for line in lines:
                cleaned_line = line.lstrip("* ").strip()
                if cleaned_line:
                    summary_line = cleaned_line
                    break
            if summary_line:
                return (
                    summary_line[:97] + "..."
                    if len(summary_line) > 100
                    else summary_line
                )
    except Exception:
        pass
    return None


# --- Markdown Formatting ---


def _format_to_markdown_recursive(node, prefix="", is_last=False):
    """Recursively formats a node and its children for Markdown output."""
    lines = []
    connector = "└── " if is_last else "├── "
    line = f"{prefix}{connector}{node['Name']}"

    if node["Type"] == "Folder":
        line += "/"

    lines.append(line)

    if node["Type"] == "Folder" and node.get("Children"):
        # Sort children by type (File, Folder) and then by name
        children = sorted(node["Children"], key=lambda x: (x["Type"], x["Name"]))
        for i, child in enumerate(children):
            is_last_child = i == len(children) - 1
            child_prefix = prefix + ("    " if is_last else "│   ")
            lines.extend(
                _format_to_markdown_recursive(
                    child, prefix=child_prefix, is_last=is_last_child
                )
            )

    return lines


def generate_markdown_structure(root_node):
    """Generates the full Markdown structure from the root node."""
    lines = [f"{root_node['Name']}/"]
    # Sort children by type (File, Folder) and then by name
    children = sorted(root_node["Children"], key=lambda x: (x["Type"], x["Name"]))
    for i, child in enumerate(children):
        is_last = i == len(children) - 1
        lines.extend(_format_to_markdown_recursive(child, prefix="", is_last=is_last))
    return "\n".join(lines)


# --- Core Traversal Logic ---


def create_structure(current_path, root_path):
    """Recursively creates a structured representation of the codebase."""
    base_name = os.path.basename(current_path)
    relative_path = os.path.relpath(current_path, root_path).replace("\\", "/")
    formatted_path = f"./{relative_path}" if relative_path != "." else "."

    if os.path.isdir(current_path):
        if base_name in FOLDERS_TO_IGNORE and base_name != "docs":
            return None

        node = {
            "Type": "Folder",
            "Name": base_name,
            "Path": formatted_path,
            "Children": [],
        }
        try:
            entries = sorted(os.listdir(current_path), key=str.lower)
        except OSError:
            return node

        for entry in entries:
            child_path = os.path.join(current_path, entry)
            child_node = create_structure(child_path, root_path)
            if child_node:
                node["Children"].append(child_node)
        return node
    else:  # It's a file
        file_name, file_ext = os.path.splitext(base_name)
        if base_name in FILE_NAMES_TO_IGNORE or file_ext in FILE_EXTENSIONS_TO_IGNORE:
            return None

        node = {"Type": "File", "Name": base_name, "Path": formatted_path}
        comment = None
        if file_ext == ".py":
            comment = get_py_comment(current_path)
        elif file_ext in [".ts", ".tsx"]:
            comment = get_ts_comment(current_path)
        if comment:
            node["comments"] = comment
        return node


# --- Main Execution ---


def main():
    """Main function to scan the codebase and export its structure."""
    parser = argparse.ArgumentParser(
        description="Scan codebase and generate a structure file."
    )
    parser.add_argument(
        "-f",
        "--format",
        nargs="+",
        default=["json", "md"],
        help="Output format (e.g., `-f json`). Default: `json md`",
    )
    args = parser.parse_args()

    print(f"Scanning codebase at: {ROOT_PATH}")
    root_name = os.path.basename(os.path.abspath(ROOT_PATH))
    root_node = {"Type": "Folder", "Name": root_name, "Path": ".", "Children": []}

    for entry in sorted(os.listdir(ROOT_PATH), key=str.lower):
        entry_path = os.path.join(ROOT_PATH, entry)
        child_node = create_structure(entry_path, ROOT_PATH)
        if child_node:
            root_node["Children"].append(child_node)

    if "json" in args.format:
        output_file_name = "codebase-structure.json"
        output_path = os.path.join(DOCS_PATH, output_file_name)
        print("Converting structure to JSON...")
        content = json.dumps(root_node, indent=4)
        if not os.path.exists(DOCS_PATH):
            print(f"Creating directory: {DOCS_PATH}")
            os.makedirs(DOCS_PATH)
        print(f"Exporting to JSON file: {output_path}")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)

    if "md" in args.format:
        output_file_name = "codebase-structure.md"
        output_path = os.path.join(DOCS_PATH, output_file_name)
        print("Formatting structure into Markdown...")
        content = generate_markdown_structure(root_node)
        if not os.path.exists(DOCS_PATH):
            print(f"Creating directory: {DOCS_PATH}")
            os.makedirs(DOCS_PATH)
        print(f"Exporting to Markdown file: {output_path}")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)

    print("Codebase structure exported successfully.")


if __name__ == "__main__":
    main()
