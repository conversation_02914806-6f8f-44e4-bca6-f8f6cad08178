# Project Documentation Wiki

## 01 — Product

**Purpose:** Capture the product's vision, goals, audience, and strategic direction. This document is the single source
of truth for the "why" and "what" of the project.

---

## How to Create Your `product.md` 🚀

Use the sections below as a guide to articulate your product vision. All entries should be concise, clear, and aligned
with your project's mission.

### 1. Product Vision & Mission Statement

- **Prompt**: Start with a clear and compelling vision statement. What is the ultimate goal of the product?
- **Prompt**: Follow up with a mission statement. What is the single most important thing the product does to achieve
  its vision?
- **Example**: "To revolutionize electrical system design by providing a unified platform for advanced calculations and
  collaboration."

### 2. Business Goals

- **Problem Statement**: Clearly define the core problem the product is solving for your users.
- **Target Outcomes (SMART goals)**: Define specific, measurable, achievable, relevant, and time-bound goals.
  - **Prompt**: "Increase user adoption by 20% within the first six months."

### 3. Audience Profiles (Persona Template)

- **Instruction**: Create profiles for your primary and secondary users. For each persona, outline:
  - **Name & Role**: E.g., '<PERSON>, Professional Electrical Engineer'
  - **Environment**: Describe their typical workflow and tools.
  - **Needs & Pain Points**: What do they need to accomplish, and what are their current frustrations?
  - **Acceptance Criteria**: What must the product do to satisfy this user's needs?

### 4. PRD (Product Requirements Document) — One-page Template

- **Instruction**: Use this template for each major feature. It ensures a consistent approach to defining requirements.
  - **Title**: Clear and descriptive name for the feature.
  - **One-line Pitch**: A brief, powerful summary of the feature.
  - **Problem**: What specific problem does this feature solve?
  - **Solution Overview**: A high-level description of the proposed solution, linking to technical designs if available.
  - **Key Metrics (KPIs)**: How will you measure the success of this feature?
  - **Non-goals**: Explicitly state what the feature will **not** do to manage scope.
  - **Timeline & Milestones**: High-level timeline for the feature's development.

---

## Common Pitfalls & Checklist ✅

- **Common Pitfalls**:
  - **Over-scoped MVP**: Attempting to include too many features in the initial release.
  - **Unclear Metrics**: Defining success without a clear way to measure it.
- **Prompts & Checklists**:
  - **Product-Market Fit Checklist**: Have we interviewed users? Does the solution solve their core pain points? Is the
    market large enough?
  - **`product.md` Checklist**: Does this document align with the project's vision? Is it clear and easy to understand
    for both technical and non-technical stakeholders? Is it up-to-date?

---
