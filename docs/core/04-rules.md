# Project Documentation Wiki

## 04 — Rules

**Purpose:** This document establishes the mandatory development rules and quality standards for the project. It defines
non-negotiable policies for code quality, security, and team workflows, ensuring the codebase remains consistent,
reliable, and maintainable. This is the **"Zero Tolerance Policy"** handbook.

---

## How to Create Your `rules.md` 👮‍♂️

Use the following sections to detail your project's strict development rules. These policies should be enforced by
automated quality gates wherever possible.

### 1. Zero Tolerance Policies (Code Quality)

- **Instruction**: Clearly state the non-negotiable rules for code quality. These should be enforced by your CI/CD
  system.
- **Content**:
  - **Linting & Formatting**: Mandate zero errors for tools like Ruff, ESLint, and Prettier. This ensures a consistent
    codebase style.
  - **Type Safety**: Require 100% compliance with type checkers such as MyPy for Python or using `strict` mode in
    TypeScript. This prevents common errors and improves code clarity.
  - **Docstring & Comments**: Specify a style guide for docstrings (e.g., Google style) and require them for all public
    methods and functions.
- **Example**:
  - **<PERSON><PERSON> (Python)**: "Zero Ruff linting errors; 100% MyPy compliance. All public methods must have Google-style
    docstrings."
  - **Frontend (TypeScript)**: "Zero ESLint errors; no `any` types in production code."

### 2. Architectural & Engineering Principles

- **Instruction**: Document the core software engineering principles that all developers must follow.
- **Content**:
  - **SOLID Principles**: Ensure developers are aware of and apply **S**ingle Responsibility, **O**pen/Closed,
    **L**iskov Substitution, **I**nterface Segregation, and **D**ependency Inversion principles.
  - **DRY (Don't Repeat Yourself)**: All code must adhere to this principle to avoid redundancy.
  - **KISS (Keep It Simple, Stupid)**: Favor simple, straightforward solutions over overly complex ones.
  - **TDD (Test-Driven Development)**: Mandate writing tests before writing the implementation code.
- **Example**: "All new feature development must begin with a failing test and adhere to the **TDD** methodology."

### 3. CI/CD & Workflow Rules

- **Instruction**: Define the rules that govern the development workflow, from `git` commits to code review.
- **Content**:
  - **Git & Commit Rules**:
    - **Branch Naming**: Enforce a convention (e.g., `feature/JIRA-123-new-auth-flow`).
    - **Conventional Commits**: Use a standard commit message format (e.g., `feat: add user registration endpoint`).
  - **Code Review Checklist**: Provide a checklist for reviewers to ensure consistency.
    - ✅ Does the code meet all **"Zero Tolerance Policies"**?
    - ✅ Are new tests included and do they pass?
    - ✅ Is the code documented and easy to understand?
    - ✅ Does the change align with the architectural design?
- **Security & Compliance**:
  - **Secrets Handling**: Describe how to manage secrets securely (e.g., using environment variables or a secret
    management service).
  - **Vulnerability Scanning**: Explain the use of tools for dependency scanning and security linting.

---

This document is the backbone of your project's quality assurance. By clearly defining these rules and automating their
enforcement, you create a robust and reliable development environment.
