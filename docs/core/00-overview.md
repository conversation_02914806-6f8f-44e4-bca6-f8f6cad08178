# Project Documentation Wiki

## 00 — Overview

**Purpose:** This document is the comprehensive entry point to the project's documentation wiki. It provides a roadmap
for navigating all project artifacts, defines the project's core terminology, and offers a clear, phase-based
methodology for both new and existing projects.

---

## Welcome to the Unified Engineering & Development Wiki 👋

This wiki provides a complete, structured, and repeatable process for managing the entire lifecycle of a **Turnkey
Project**. A turnkey project is a fully self-contained product delivery that can be handed over to a client or operator
and run with minimal onboarding.

This documentation serves as the single source of truth for all project decisions, standards, and methodologies. **All
documentation is mandatory and must be adhered to.**

---

## How to Navigate This Wiki

This documentation is organized into **ten sequential phases**, numbered `00` through `09`. Each notebook builds upon
the last, guiding you from a high-level idea to a production-ready release.

- **Follow the Numerical Order**: Start with `00 — Overview` and progress sequentially to `09 — Release`.
- **Use Templates**: Each notebook provides checklists, templates, and prompts as actionable starting points. Copy and
  paste them into your own project's documentation.
- **Reference the Glossary**: Use the `Glossary` section below to understand key project terminology.

---

## The 10-Phase Project Methodology 🗺️

This phase map represents the full project lifecycle. Each phase corresponds to a notebook in this wiki.

1. **`00 — Overview`**: You are here. Understand the project's purpose and how to use this documentation.
2. **`01 — Product`**: Define the **"What and Why"**. Capture business goals, audience, and the product vision.
3. **`02 — Structure`**: Define the **"Blueprint"**. Detail the architectural patterns and repository layout.
4. **`03 — Tech`**: Define the **"Tools"**. Specify the full technology stack and key technical choices.
5. **`04 — Rules`**: Define the **"Standards"**. Establish mandatory coding standards and quality gates.
6. **`05 — Requirements`**: Define the **"Specs"**. Write all functional and non-functional requirements.
7. **`06 — Design`**: Define the **"How"**. Create the technical design, including the API and database schemas.
8. **`07 — Tasks`**: Define the **"Work"**. Break down the design into a detailed, actionable task plan.
9. **`08 — Workflows`**: Define the **"Process"**. Document all operational procedures for development and deployment.
10. **`09 — Release`**: Define the **"Handover"**. Create the release checklist, rollback plan, and handover
    documentation.

---

## Quickstart Guide for New Projects 🚀

If you are starting a new project, follow these three steps to build a solid foundation.

1. **Define the Product**: Complete the templates in `01-Product` to establish clear business goals and target
   audiences.
2. **Sketch the Architecture**: Use the checklist in `02-Structure` to lay out your project's high-level architecture.
3. **Plan the First Sprint**: Use the templates in `07-Tasks` to create a backlog for your first sprint, ensuring
   requirements are linked to actionable work.

---

## Starter Prompts & Templates

Use these prompts to jumpstart your documentation creation:

- "Create a 1-page PRD for an **electrical design platform** aimed at **professional engineers**." (from `01-Product`)
- "List minimal viable infrastructure options for a web app with **1,000 concurrent users**." (from `03-Tech`)

---

## Glossary

- **Turnkey Project**: A fully self-contained, ready-to-run product.
- **Monorepo**: A single repository hosting multiple, independent projects.
- **EARS**: Easy Approach to Requirements Syntax, a standardized format for writing clear requirements.
- **CI/CD Quality Gates**: Automated checks that enforce quality standards on every code change.
- **TDD**: Test-Driven Development, a methodology where tests are written before implementation code.

---
