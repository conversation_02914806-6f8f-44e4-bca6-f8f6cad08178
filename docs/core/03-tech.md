# Project Documentation Wiki

## 03 — Tech

**Purpose:** This document provides a comprehensive overview of the project's technology stack. It details the rationale
behind key technology choices, specifies versions, and outlines the workflows for managing dependencies and releases.
This document serves as a critical reference for all development and DevOps activities.

---

## How to Create Your `tech.md` 💻

Use the sections below to document your project's technology stack. Be precise with versions and provide clear
justifications for all choices.

### 1. Technology Stack Overview

- **Instruction**: Begin with a high-level summary of your entire technology stack.
- **Example**: "The project uses a Python FastAPI backend and a Next.js (React) frontend. The system relies on
  PostgreSQL for the database and SQLAlchemy as the ORM."

### 2. Technology Stack Decisions

- **Instruction**: For each core technology, explain the decision-making process. Use a decision matrix or similar
  framework to justify your choices.
- **Decision Matrix Fields**:
  - **Team Familiarity**: How well does the team know this technology?
  - **Ecosystem Maturity**: Is the technology well-supported with good documentation and a strong community?
  - **Performance**: Does it meet the project's performance requirements?
  - **Cost & Time-to-Market**: How does it impact development speed and operational costs?
- **Example - Backend Framework**:
  - **Choice**: FastAPI
  - **Rationale**: Selected for its high performance (`async/await`), built-in validation, and automatic API
    documentation (`OpenAPI/Swagger`). Its use of standard Python type hints aligns with the project's "Zero Tolerance
    Policies."

### 3. Hosting & Infrastructure Options

- **Instruction**: Document your choices for hosting and infrastructure.
- **Content**:
  - **Serverless**: For functions that require infrequent, scalable execution.
  - **Containers**: For the main backend and frontend services using Docker. Explain the benefits of containerization
    (e.g., consistency across environments).
  - **Managed VMs**: For dedicated, long-running services.
  - **PaaS (Platform as a Service)**: Mention if you use services like Vercel for the frontend or Heroku for the
    backend, and explain why.

### 4. Dependency & Release Workflows

- **Instruction**: Define the processes for managing project dependencies and releases to ensure stability and security.
- **Content**:
  - **Lockfiles**: Emphasize the mandatory use of lockfiles (`pnpm-lock.yaml`, `poetry.lock`) to ensure reproducible
    builds across all environments.
  - **Semantic Versioning**: Explain the project's adherence to SemVer for all packages and public APIs.
  - **Dependency Scanning**: Describe the use of tools for automated vulnerability scanning of dependencies.
- **Prompts**:
  - **Prompt**: "Outline the dependency management strategy for a new microservice. How will you handle updates and
    security patches?"
  - **Template**: Use a standardized format for listing dependencies, including package name, version, and a brief
    description.

---

This detailed approach ensures that all technical decisions are transparent, justifiable, and well-documented, providing
a solid foundation for the project's long-term health and scalability.
